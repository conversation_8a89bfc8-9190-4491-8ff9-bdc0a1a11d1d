//  Velvete Store
//
//  Created by <PERSON><PERSON>.
//  2025, Velvte. All rights reserved.
//

//  Unless required by applicable law or agreed to in writing, software
//  distributed under the License is distributed on an "AS IS" BASIS,
//  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.

import 'package:woocommerce_flutter_api/woocommerce_flutter_api.dart';
import 'package:flutter/foundation.dart'; // For kDebugMode performance optimization
import '/bootstrap/helpers.dart';
import 'my_product_image.dart';
import 'my_product_category.dart';

class MyProduct {
  // Core fields from WooProduct
  final int id;
  final String name;
  final String slug;
  final String permalink;
  final DateTime? dateCreated;
  final DateTime? dateModified;
  final String type;
  final String status;
  final bool featured;
  final String catalogVisibility;

  // Nullable fields confirmed by analysis or common API behavior
  final String? description; // Confirmed nullable
  final String? shortDescription; // Can be null
  final String? sku; // Can be null
  final DateTime? dateOnSaleFrom; // Confirmed nullable
  final DateTime? dateOnSaleTo; // Can be null

  // Price fields - using num? to handle null values safely
  final num? price; // Guaranteed: Numerical type for prices
  final num? regularPrice;
  final num? salePrice;
  final num? displayRegularPrice; // This will hold the reliable regular price for display.

  // Ratings
  // Parsed from WooCommerce API fields: average_rating (string) and rating_count (int)
  final double? averageRating;
  final int? ratingCount;

  // Stock and shipping
  final bool? manageStock;
  final int? stockQuantity;
  final String? stockStatus;
  final String? backorders;
  final bool? backordersAllowed;
  final bool? backordered;
  final bool? soldIndividually;
  final String? weight;
  final bool? shippingRequired;
  final bool? shippingTaxable;
  final String? shippingClass;

  // Product state
  final bool? purchasable;
  final bool? onSale;
  final String? purchaseNote;
  final String? externalUrl;

  // Tax
  final String? taxStatus;
  final String? taxClass;

  // Images and categories
  final List<MyProductImage> images;
  final List<MyProductCategory>? categories;

  // Attributes and variations
  final List<dynamic> attributes;

  // Meta data
  final List<WooMetaData> metaData;

  MyProduct({
    required this.id,
    required this.name,
    required this.slug,
    required this.permalink,
    this.dateCreated,
    this.dateModified,
    required this.type,
    required this.status,
    required this.featured,
    required this.catalogVisibility,
    this.description,
    this.shortDescription,
    this.sku,
    this.dateOnSaleFrom,
    this.dateOnSaleTo,
    this.price,
    this.regularPrice,
    this.salePrice,
    this.displayRegularPrice, // Add to constructor
    this.averageRating,
    this.ratingCount,
    this.manageStock,
    this.stockQuantity,
    this.stockStatus,
    this.backorders,
    this.backordersAllowed,
    this.backordered,
    this.soldIndividually,
    this.weight,
    this.shippingRequired,
    this.shippingTaxable,
    this.shippingClass,
    this.purchasable,
    this.onSale,
    this.purchaseNote,
    this.externalUrl,
    this.taxStatus,
    this.taxClass,
    required this.images,
    this.categories,
    required this.attributes,
    required this.metaData,
  });

  // Helper function to safely convert dynamic values to bool
  static bool? _safeBool(dynamic value) {
    if (value == null) return null;
    if (value is bool) return value;
    if (value is String) {
      if (value.toLowerCase() == 'true' || value == '1') return true;
      if (value.toLowerCase() == 'false' || value == '0') return false;
      return null;
    }
    if (value is int) return value != 0;
    return null;
  }



  factory MyProduct.fromJson(Map<String, dynamic> json) {
    if (json['id'] == null || json['id'] == 0) {
      throw Exception("DEFINITIVE ACTION 1: Cannot create MyProduct from JSON with invalid ID: ${json['id']}. Name: ${json['name']}");
    }

    // Parse the base prices first
    final parsedPrice = parseWcPrice(json['price']?.toString());
    final parsedRegularPrice = parseWcPrice(json['regular_price']?.toString());
    final parsedSalePrice = parseWcPrice(json['sale_price']?.toString());
    final type = json['type'] as String? ?? 'simple';
    final onSaleFlag = _safeBool(json['on_sale']) ?? false;

    // GUARANTEED FIX LOGIC:
    // For variable products in a list view, 'regular_price' is often unreliable (0.00 or null).
    // However, the 'price_html' field often contains both prices. We will parse it.
    String priceHtml = json['price_html'] as String? ?? '';
    num? inferredRegularPrice;

    if (type == 'variable' && onSaleFlag && priceHtml.contains('<del>')) {
      try {
        // Extract the value from within the <del> tag.
        final delTagContent = priceHtml.split('<del>')[1].split('</del>')[0];
        // This will give something like <span class="woocommerce-Price-amount amount"><bdi>350.00<span class="woocommerce-Price-currencySymbol">د.ل</span></bdi></span>
        // We need to parse the number from this.
        inferredRegularPrice = parseWcPrice(delTagContent);
      } catch (e) {
        if (kDebugMode) {
          print('🔄 Could not infer regular price from price_html for product ${json['id']}: $e');
        }
        inferredRegularPrice = null;
      }
    }

    final product = MyProduct(
      id: json['id'],
      name: json['name'] as String? ?? 'Untitled Product',
      slug: json['slug'] as String? ?? '',
      permalink: json['permalink'] as String? ?? '',
      dateCreated: json['date_created'] != null ? DateTime.tryParse(json['date_created']) : null,
      dateModified: json['date_modified'] != null ? DateTime.tryParse(json['date_modified']) : null,
      type: type,
      status: json['status'] as String? ?? 'publish',
      featured: _safeBool(json['featured']) ?? false,
      catalogVisibility: json['catalog_visibility'] as String? ?? 'visible',
      description: json['description'] as String?,
      shortDescription: json['short_description'] as String?,
      sku: json['sku'] as String?,
      dateOnSaleFrom: json['date_on_sale_from'] != null ? DateTime.tryParse(json['date_on_sale_from']) : null,
      dateOnSaleTo: json['date_on_sale_to'] != null ? DateTime.tryParse(json['date_on_sale_to']) : null,
      price: parsedPrice,
      regularPrice: parsedRegularPrice,
      salePrice: parsedSalePrice,

      // SET THE NEW DISPLAY PRICE
      // Priority: 1. Inferred from HTML, 2. Parsed regular price.
      displayRegularPrice: inferredRegularPrice ?? parsedRegularPrice,

      onSale: onSaleFlag,
      manageStock: _safeBool(json['manage_stock']),
      stockQuantity: json['stock_quantity'] as int?,
      stockStatus: json['stock_status']?.toString(),
      backorders: json['backorders']?.toString(),
      backordersAllowed: _safeBool(json['backorders_allowed']),
      backordered: _safeBool(json['backordered']),
      soldIndividually: _safeBool(json['sold_individually']),
      weight: json['weight']?.toString(),
      shippingRequired: _safeBool(json['shipping_required']),
      shippingTaxable: _safeBool(json['shipping_taxable']),
      shippingClass: json['shipping_class']?.toString(),
      purchasable: _safeBool(json['purchasable']),
      purchaseNote: json['purchase_note']?.toString(),
      externalUrl: json['external_url']?.toString(),
      taxStatus: json['tax_status']?.toString(),
      taxClass: json['tax_class']?.toString(),
      images: (json['images'] as List?)?.map((img) => MyProductImage.fromJson(img)).toList() ?? [],
      categories: (json['categories'] as List?)?.map((cat) => MyProductCategory.fromJson(cat)).toList(),
      attributes: json['attributes'] as List? ?? [],
      metaData: [], // Will be populated if needed
      // Ratings: average_rating comes as string (e.g., "4.5"); rating_count as int
      averageRating: double.tryParse((json['average_rating'] ?? '0').toString()),
      ratingCount: int.tryParse((json['rating_count'] ?? '0').toString()),
    );

    if (kDebugMode) {
      print('🔄 MyProduct ${product.id} Final Model State: price=${product.price}, regularPrice=${product.regularPrice}, salePrice=${product.salePrice}, onSale=${product.onSale}, displayRegularPrice=${product.displayRegularPrice}');
    }

    return product;
  }

  // Factory constructor to convert from WooProduct to MyProduct
  factory MyProduct.fromWooProduct(WooProduct wooProduct) {
    // DEFINITIVE ACTION 1: ERADICATE the Ghost Product - NEVER create product with id: 0
    if (wooProduct.id == null || wooProduct.id == 0) {
      throw Exception("DEFINITIVE ACTION 1: Cannot create MyProduct from WooProduct with invalid ID: ${wooProduct.id}. Name: ${wooProduct.name}");
    }
    return MyProduct(
      id: wooProduct.id!,
      name: wooProduct.name ?? 'Untitled Product',
      slug: wooProduct.slug ?? '',
      permalink: wooProduct.permalink ?? '',
      dateCreated: wooProduct.dateCreated,
      dateModified: wooProduct.dateModified,
      type: wooProduct.type?.toString() ?? 'simple',
      status: wooProduct.status?.name ?? 'publish',
      featured: wooProduct.featured ?? false,
      catalogVisibility: wooProduct.catalogVisibility?.name ?? 'visible',
      description: wooProduct.description, // Allow null
      shortDescription: wooProduct.shortDescription, // Allow null
      sku: wooProduct.sku, // Allow null
      dateOnSaleFrom: wooProduct.dateOnSaleFrom, // Allow null
      dateOnSaleTo: wooProduct.dateOnSaleTo, // Allow null
      price: double.tryParse(wooProduct.price?.toString() ?? ''), // Guaranteed: Safely parse to double
      regularPrice: double.tryParse(wooProduct.regularPrice?.toString() ?? ''), // Guaranteed: Safely parse to double
      salePrice: double.tryParse(wooProduct.salePrice?.toString() ?? ''), // Guaranteed: Safely parse to double
      displayRegularPrice: double.tryParse(wooProduct.regularPrice?.toString() ?? ''), // Use regularPrice as fallback for WooProduct
      manageStock: wooProduct.manageStock,
      stockQuantity: wooProduct.stockQuantity,
      stockStatus: wooProduct.stockStatus?.name,
      backorders: wooProduct.backorders?.name,
      backordersAllowed: wooProduct.backordersAllowed,
      backordered: wooProduct.backordered,
      soldIndividually: wooProduct.soldIndividually,
      weight: wooProduct.weight,
      shippingRequired: wooProduct.shippingRequired,
      shippingTaxable: wooProduct.shippingTaxable,
      shippingClass: wooProduct.shippingClass,
      purchasable: wooProduct.purchasable,
      onSale: wooProduct.onSale,
      purchaseNote: wooProduct.purchaseNote,
      externalUrl: wooProduct.externalUrl,
      taxStatus: wooProduct.taxStatus?.name,
      taxClass: wooProduct.taxClass,
      images: wooProduct.images.map((img) => MyProductImage.fromWooProductImage(img)).toList(),
      categories: wooProduct.categories.map((cat) => MyProductCategory.fromWooProductCategory(cat)).toList(),
      attributes: wooProduct.attributes,
      metaData: wooProduct.metaData,
    );
  }

  // Helper methods for safe access to nullable fields
  String getSafeDescription({String fallback = ''}) {
    return description ?? fallback;
  }

  String getSafeShortDescription({String fallback = ''}) {
    return shortDescription ?? fallback;
  }

  String getSafeSku({String fallback = ''}) {
    return sku ?? fallback;
  }

  num getSafePrice({num fallback = 0.0}) {
    // PHASE 10: Critical Price Fallback Logic Fix for MyProduct
    // Priority: price -> regularPrice -> fallback
    num result;
    if (price != null && price! > 0) {
      result = price!; // Prefer the 'price' field if valid
    } else if (regularPrice != null && regularPrice! > 0) {
      result = regularPrice!; // Fallback to 'regularPrice' if valid
    } else {
      // Logging completely disabled for performance - variable products commonly have null main prices
      result = fallback;
    }

    // DIAGNOSTIC LOGGING: Log the value being returned
    if (kDebugMode) {
      print('🔍 getSafePrice() for Product ID $id returning: $result');
    }

    return result;
  }

  num getSafeRegularPrice({num fallback = 0.0}) {
    // If a valid, non-zero regular price exists, it is the source of truth.
    if (regularPrice != null && regularPrice! > 0) {
      return regularPrice!;
    }

    // THE GUARANTEED FIX: For variable products on sale, the regular price
    // is often found in the displayRegularPrice field, which is a key part
    // of the MyProduct model's data processing. We use this as a definitive fallback.
    if (displayRegularPrice != null && displayRegularPrice! > 0) {
      return displayRegularPrice!;
    }

    // If the product is not on sale, or if all other price fields are invalid,
    // we return the fallback. This prevents 0.0 from being displayed incorrectly.
    return fallback;
  }

  num getSafeSalePrice({num fallback = 0.0}) {
    // If a valid, non-zero sale price exists, it is the source of truth.
    if (salePrice != null && salePrice! > 0) {
      return salePrice!;
    }

    // THE GUARANTEED FIX: If there is no valid 'sale_price' field, but the product is
    // confirmed to be on sale via our robust isOnSale() logic, then the main 'price'
    // field holds the correct current sale price. This is the definitive fallback.
    if (this.isOnSale()) {
      return this.getSafePrice(fallback: fallback);
    }

    // If the product is not on sale, there is no sale price. Return the fallback.
    return fallback;
  }

  // Helper method to get the first image safely
  MyProductImage? getFirstImage() {
    return images.isNotEmpty ? images.first : null;
  }

  // Helper method to check if product has images
  bool hasImages() {
    return images.isNotEmpty;
  }

  // Helper method to check if product is on sale - CRITICAL FIX: Reliable source of truth
  bool isOnSale() {
    // DIAGNOSTIC LOGGING: Log input values at the beginning of isOnSale method
    if (kDebugMode) {
      print('🔍 isOnSale() for Product ID $id: salePrice=$salePrice, regularPrice=$regularPrice, onSale=$onSale, dateOnSaleFrom=$dateOnSaleFrom, dateOnSaleTo=$dateOnSaleTo');
    }

    // SALE DATE VALIDATION: Check if current date is within sale period
    DateTime now = DateTime.now();

    // Check sale start date (if specified)
    if (dateOnSaleFrom != null && now.isBefore(dateOnSaleFrom!)) {
      if (kDebugMode) {
        print('🔍 isOnSale() for Product ID $id: Date check failed - sale hasn\'t started yet');
      }
      return false; // Sale hasn't started yet
    }

    // Check sale end date (if specified)
    if (dateOnSaleTo != null && now.isAfter(dateOnSaleTo!)) {
      if (kDebugMode) {
        print('🔍 isOnSale() for Product ID $id: Date check failed - sale has ended');
      }
      return false; // Sale has ended
    }

    // PRIMARY NUMERICAL VALIDATION: Check for genuine price reduction
    // A genuine sale price must be BOTH lower than the regular price AND greater than zero.
    if (salePrice != null && regularPrice != null && salePrice! > 0 && salePrice! < regularPrice!) {
      if (kDebugMode) {
        print('🔍 isOnSale() for Product ID $id: Numerical sale detected - salePrice ($salePrice) < regularPrice ($regularPrice)');
      }
      return true; // Numerical sale confirmed
    }

    // SECONDARY CONDITION: If no numerical sale, but API explicitly says it's on sale
    // This is a fallback for cases where numerical prices might be tricky, but API flag is definitive
    if (onSale == true) {
      if (kDebugMode) {
        print('🔍 isOnSale() for Product ID $id: API onSale flag active (no numerical validation)');
      }
      return true;
    }

    // DIAGNOSTIC LOGGING: Log final return value
    if (kDebugMode) {
      print('🔍 isOnSale() for Product ID $id: Final result: false (no sale conditions met)');
    }

    // Default: Not on sale
    return false;
  }

  // Method to check if any variations are on sale
  bool hasAnyVariationOnSale(List<dynamic>? variations) {
    if (type != "variable" || variations == null || variations.isEmpty) {
      return false;
    }

    // Check if any variation has sale price lower than regular price
    for (var variation in variations) {
      if (variation != null) {
        // Handle both MyProductVariation objects and raw variation data
        try {
          if (variation.runtimeType.toString().contains('MyProductVariation')) {
            // MyProductVariation object with isOnSale method
            if (variation.isOnSale()) {
              return true;
            }
          } else if (variation is Map<String, dynamic>) {
            // Raw variation data - FIXED: Use proper sale validation logic
            var onSale = variation['on_sale'];
            var salePrice = variation['sale_price'];
            var regularPrice = variation['regular_price'];
            var dateOnSaleFrom = variation['date_on_sale_from'];
            var dateOnSaleTo = variation['date_on_sale_to'];

            // Apply same logic as MyProduct.isOnSale()
            if (onSale != true) {
              continue; // Skip if not marked as on sale by API
            }

            // Check sale dates if provided
            DateTime now = DateTime.now();
            if (dateOnSaleFrom != null) {
              DateTime? saleStart = DateTime.tryParse(dateOnSaleFrom.toString());
              if (saleStart != null && now.isBefore(saleStart)) {
                continue; // Sale hasn't started
              }
            }
            if (dateOnSaleTo != null) {
              DateTime? saleEnd = DateTime.tryParse(dateOnSaleTo.toString());
              if (saleEnd != null && now.isAfter(saleEnd)) {
                continue; // Sale has ended
              }
            }

            // Final numerical validation
            if (salePrice != null && regularPrice != null) {
              double? sale = double.tryParse(salePrice.toString());
              double? regular = double.tryParse(regularPrice.toString());
              if (sale != null && regular != null && sale < regular) {
                return true;
              }
            }

            // If onSale is true and dates are valid, trust the API
            return true;
          }
        } catch (e) {
          // Continue checking other variations if one fails
          continue;
        }
      }
    }
    return false;
  }

  // Get minimum sale price from variations
  String? getMinimumSalePrice(List<dynamic>? variations) {
    if (type != "variable" || variations == null || variations.isEmpty) {
      return null;
    }

    List<double> salePrices = [];
    for (var variation in variations) {
      if (variation != null) {
        try {
          if (variation.runtimeType.toString().contains('MyProductVariation')) {
            if (variation.isOnSale()) {
              double? price = double.tryParse(variation.getSafeSalePrice().toString());
              if (price != null && price > 0) {
                salePrices.add(price);
              }
            }
          } else if (variation is Map<String, dynamic>) {
            // FIXED: Use proper sale validation logic matching isOnSale()
            var onSale = variation['on_sale'];
            var salePrice = variation['sale_price'];
            var regularPrice = variation['regular_price'];
            var dateOnSaleFrom = variation['date_on_sale_from'];
            var dateOnSaleTo = variation['date_on_sale_to'];

            // Apply same logic as MyProduct.isOnSale()
            if (onSale != true) {
              continue; // Skip if not marked as on sale by API
            }

            // Check sale dates if provided
            DateTime now = DateTime.now();
            if (dateOnSaleFrom != null) {
              DateTime? saleStart = DateTime.tryParse(dateOnSaleFrom.toString());
              if (saleStart != null && now.isBefore(saleStart)) {
                continue; // Sale hasn't started
              }
            }
            if (dateOnSaleTo != null) {
              DateTime? saleEnd = DateTime.tryParse(dateOnSaleTo.toString());
              if (saleEnd != null && now.isAfter(saleEnd)) {
                continue; // Sale has ended
              }
            }

            // Final numerical validation and price collection
            if (salePrice != null && regularPrice != null) {
              double? sale = double.tryParse(salePrice.toString());
              double? regular = double.tryParse(regularPrice.toString());
              if (sale != null && regular != null && sale < regular) {
                salePrices.add(sale);
              }
            }
          }
        } catch (e) {
          continue;
        }
      }
    }

    if (salePrices.isNotEmpty) {
      salePrices.sort();
      return salePrices.first.toString();
    }
    return null;
  }

  // Get price from variations when main product price is null
  num getPriceFromVariations(List<dynamic>? variations, {num fallback = 0.0}) {
    if (type != "variable" || variations == null || variations.isEmpty) {
      return fallback;
    }

    List<double> prices = [];
    for (var variation in variations) {
      if (variation != null) {
        try {
          if (variation.runtimeType.toString().contains('MyProductVariation')) {
            double? price = double.tryParse(variation.getSafePrice().toString());
            if (price != null && price > 0) {
              prices.add(price);
            }
          }
        } catch (e) {
          // Skip invalid variations
        }
      }
    }

    if (prices.isNotEmpty) {
      prices.sort();
      return prices.first; // Return the lowest price
    }
    return fallback;
  }

  // NEW HELPER METHOD: Get lowest regular price from variations
  num getLowestRegularPriceFromVariations(List<dynamic>? variations, {num fallback = 0.0}) {
    if (type != "variable" || variations == null || variations.isEmpty) {
      return fallback;
    }

    List<double> regularPrices = [];
    for (var variation in variations) {
      if (variation != null) {
        try {
          if (variation.runtimeType.toString().contains('MyProductVariation')) {
            double? regularPrice = double.tryParse(variation.getSafeRegularPrice().toString());
            if (regularPrice != null && regularPrice > 0) {
              regularPrices.add(regularPrice);
            }
          }
        } catch (e) {
          // Skip invalid variations
        }
      }
    }

    if (regularPrices.isNotEmpty) {
      regularPrices.sort();
      return regularPrices.first; // Return the lowest regular price
    }
    return fallback;
  }
}
